#!/usr/bin/env python3
"""
数据集准备脚本
将标注数据和图片整理成YOLO训练格式，并进行训练/验证集划分
"""

import os
import shutil
import random
from pathlib import Path
from loguru import logger
import yaml


def create_yolo_dataset_structure(base_path: str = "data/yolo_dataset"):
    """创建YOLO数据集目录结构"""
    base_path = Path(base_path)
    
    # 创建目录结构
    dirs_to_create = [
        base_path / "train" / "images",
        base_path / "train" / "labels", 
        base_path / "val" / "images",
        base_path / "val" / "labels"
    ]
    
    for dir_path in dirs_to_create:
        dir_path.mkdir(parents=True, exist_ok=True)
        logger.info(f"创建目录: {dir_path}")
    
    return base_path


def split_dataset(image_files: list, train_ratio: float = 0.8):
    """划分训练集和验证集"""
    random.shuffle(image_files)
    split_idx = int(len(image_files) * train_ratio)
    
    train_files = image_files[:split_idx]
    val_files = image_files[split_idx:]
    
    logger.info(f"数据集划分: 训练集 {len(train_files)} 张, 验证集 {len(val_files)} 张")
    return train_files, val_files


def copy_files_to_yolo_structure(source_images_dir: str, source_labels_dir: str, 
                                target_base_dir: Path, train_files: list, val_files: list):
    """将文件复制到YOLO目录结构中"""
    
    def copy_file_set(file_list: list, subset: str):
        """复制指定文件集合"""
        for filename in file_list:
            # 复制图片
            src_img = Path(source_images_dir) / f"{filename}.jpg"
            dst_img = target_base_dir / subset / "images" / f"{filename}.jpg"
            if src_img.exists():
                shutil.copy2(src_img, dst_img)
            
            # 复制标签
            src_label = Path(source_labels_dir) / f"{filename}.txt"
            dst_label = target_base_dir / subset / "labels" / f"{filename}.txt"
            if src_label.exists():
                shutil.copy2(src_label, dst_label)
            else:
                logger.warning(f"标签文件不存在: {src_label}")
    
    # 复制训练集
    logger.info("复制训练集文件...")
    copy_file_set(train_files, "train")
    
    # 复制验证集
    logger.info("复制验证集文件...")
    copy_file_set(val_files, "val")


def create_data_yaml(dataset_path: Path, class_names: list):
    """创建data.yaml配置文件"""
    data_config = {
        'path': str(dataset_path.absolute()),
        'train': 'train/images',
        'val': 'val/images',
        'nc': len(class_names),
        'names': {i: name for i, name in enumerate(class_names)}
    }
    
    yaml_path = dataset_path / "data.yaml"
    with open(yaml_path, 'w', encoding='utf-8') as f:
        yaml.dump(data_config, f, default_flow_style=False, allow_unicode=True)
    
    logger.success(f"创建data.yaml配置文件: {yaml_path}")
    return yaml_path


def analyze_dataset(labels_dir: str, class_names: list):
    """分析数据集统计信息"""
    logger.info("分析数据集统计信息...")
    
    class_counts = {name: 0 for name in class_names}
    total_objects = 0
    files_with_objects = 0
    
    for label_file in Path(labels_dir).glob("*.txt"):
        if label_file.name == "classes.txt":
            continue
            
        with open(label_file, 'r') as f:
            lines = f.readlines()
            
        if lines:
            files_with_objects += 1
            for line in lines:
                if line.strip():
                    class_id = int(line.split()[0])
                    if class_id < len(class_names):
                        class_counts[class_names[class_id]] += 1
                        total_objects += 1
    
    logger.info("数据集统计:")
    logger.info(f"  总文件数: {len(list(Path(labels_dir).glob('*.txt'))) - 1}")  # -1 for classes.txt
    logger.info(f"  有标注的文件: {files_with_objects}")
    logger.info(f"  总对象数: {total_objects}")
    logger.info("  各类别分布:")
    for class_name, count in class_counts.items():
        logger.info(f"    {class_name}: {count}")
    
    return class_counts


def main():
    """主函数"""
    logger.info("🚀 开始准备YOLO训练数据集")
    
    # 配置路径
    source_images_dir = "data/extracted_frames"
    source_labels_dir = "data/annotated_frames"
    target_dataset_dir = "data/yolo_dataset"
    
    # 读取类别名称
    classes_file = Path(source_labels_dir) / "classes.txt"
    if not classes_file.exists():
        logger.error(f"类别文件不存在: {classes_file}")
        return
    
    with open(classes_file, 'r') as f:
        class_names = [line.strip() for line in f.readlines() if line.strip()]
    
    logger.info(f"检测到类别: {class_names}")
    
    # 分析数据集
    analyze_dataset(source_labels_dir, class_names)
    
    # 获取所有图片文件名（不含扩展名）
    image_files = []
    for img_file in Path(source_images_dir).glob("*.jpg"):
        filename = img_file.stem
        # 检查对应的标签文件是否存在
        label_file = Path(source_labels_dir) / f"{filename}.txt"
        if label_file.exists():
            image_files.append(filename)
        else:
            logger.warning(f"图片 {img_file} 没有对应的标签文件")
    
    logger.info(f"找到 {len(image_files)} 个有效的图片-标签对")
    
    if len(image_files) == 0:
        logger.error("没有找到有效的图片-标签对，请检查文件路径")
        return
    
    # 创建YOLO数据集目录结构
    dataset_path = create_yolo_dataset_structure(target_dataset_dir)
    
    # 划分数据集（小数据集使用更高的训练比例）
    train_ratio = 0.85 if len(image_files) < 100 else 0.8
    train_files, val_files = split_dataset(image_files, train_ratio)
    
    # 复制文件到YOLO结构
    copy_files_to_yolo_structure(
        source_images_dir, source_labels_dir, 
        dataset_path, train_files, val_files
    )
    
    # 创建data.yaml配置文件
    yaml_path = create_data_yaml(dataset_path, class_names)
    
    logger.success("✅ 数据集准备完成!")
    logger.info(f"数据集路径: {dataset_path}")
    logger.info(f"配置文件: {yaml_path}")
    logger.info(f"训练集: {len(train_files)} 张图片")
    logger.info(f"验证集: {len(val_files)} 张图片")
    
    # 验证数据集结构
    logger.info("\n📁 数据集目录结构:")
    for root, dirs, files in os.walk(dataset_path):
        level = root.replace(str(dataset_path), '').count(os.sep)
        indent = ' ' * 2 * level
        logger.info(f"{indent}{os.path.basename(root)}/")
        subindent = ' ' * 2 * (level + 1)
        for file in files[:3]:  # 只显示前3个文件
            logger.info(f"{subindent}{file}")
        if len(files) > 3:
            logger.info(f"{subindent}... 还有 {len(files) - 3} 个文件")


if __name__ == "__main__":
    # 设置随机种子以确保可重复性
    random.seed(42)
    main()
