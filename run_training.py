#!/usr/bin/env python3
"""
一键训练脚本
自动完成数据准备和模型训练的完整流程
"""

import subprocess
import sys
from pathlib import Path
from loguru import logger
import argparse


def run_command(command: str, description: str, show_realtime: bool = False):
    """运行命令并处理结果"""
    logger.info(f"🔄 {description}")
    logger.info(f"执行命令: {command}")

    try:
        if show_realtime:
            # 实时显示输出
            return run_command_realtime(command, description)
        else:
            # 传统方式，等待完成后显示结果
            result = subprocess.run(command, shell=True, check=True,
                                  capture_output=True, text=True)
            logger.success(f"✅ {description} 完成")
            if result.stdout:
                logger.info("输出:")
                for line in result.stdout.strip().split('\n'):
                    logger.info(f"  {line}")
            return True
    except subprocess.CalledProcessError as e:
        logger.error(f"❌ {description} 失败")
        logger.error(f"错误代码: {e.returncode}")
        if e.stdout:
            logger.error("标准输出:")
            for line in e.stdout.strip().split('\n'):
                logger.error(f"  {line}")
        if e.stderr:
            logger.error("错误输出:")
            for line in e.stderr.strip().split('\n'):
                logger.error(f"  {line}")
        return False


def run_command_realtime(command: str, description: str):
    """实时显示命令输出"""
    import threading
    import time
    from queue import Queue, Empty

    logger.info(f"🚀 开始执行: {description}")
    logger.info("=" * 60)

    def enqueue_output(out, queue):
        """将输出放入队列"""
        for line in iter(out.readline, b''):
            queue.put(line)
        out.close()

    def monitor_progress():
        """监控训练进度"""
        start_time = time.time()
        last_update = start_time
        last_output_time = start_time

        while process.poll() is None:
            current_time = time.time()
            elapsed = current_time - start_time

            # 每30秒显示一次状态
            if current_time - last_update >= 30:
                logger.info(f"⏱️ 运行时间: {elapsed/60:.1f} 分钟 - 进程仍在运行...")

                # 检查是否长时间没有输出
                if current_time - last_output_time > 120:  # 2分钟没有输出
                    logger.warning("⚠️ 长时间没有新输出，但进程仍在运行...")
                    logger.info("这可能是正常的，训练过程中某些阶段会比较安静")

                last_update = current_time

            time.sleep(5)

    try:
        # 启动进程
        process = subprocess.Popen(
            command,
            shell=True,
            stdout=subprocess.PIPE,
            stderr=subprocess.STDOUT,
            universal_newlines=True,
            bufsize=1
        )

        # 启动输出队列
        q = Queue()
        t = threading.Thread(target=enqueue_output, args=(process.stdout, q))
        t.daemon = True
        t.start()

        # 启动进度监控
        monitor_thread = threading.Thread(target=monitor_progress)
        monitor_thread.daemon = True
        monitor_thread.start()

        # 实时读取并显示输出
        last_line_time = time.time()
        while True:
            try:
                line = q.get_nowait()
            except Empty:
                # 检查进程是否结束
                if process.poll() is not None:
                    break
                time.sleep(0.1)
                continue

            # 处理输出行
            line = line.strip()
            if line:
                last_line_time = time.time()

                # 根据内容类型使用不同的日志级别和图标
                line_lower = line.lower()

                if any(keyword in line_lower for keyword in ['error', 'failed', 'exception', 'traceback']):
                    logger.error(f"❌ {line}")
                elif any(keyword in line_lower for keyword in ['warning', 'warn']):
                    logger.warning(f"⚠️ {line}")
                elif 'epoch' in line_lower and any(metric in line_lower for metric in ['loss', 'map', 'precision', 'recall']):
                    logger.info(f"📊 {line}")
                elif any(keyword in line_lower for keyword in ['downloading', 'loading', 'model']):
                    logger.info(f"📥 {line}")
                elif any(keyword in line_lower for keyword in ['training', 'train']):
                    logger.info(f"🎯 {line}")
                elif any(keyword in line_lower for keyword in ['validating', 'val']):
                    logger.info(f"🔍 {line}")
                elif any(keyword in line_lower for keyword in ['saved', 'complete', 'finished', 'success']):
                    logger.success(f"✅ {line}")
                elif any(keyword in line_lower for keyword in ['optimizer', 'lr', 'learning']):
                    logger.info(f"⚙️ {line}")
                elif 'results saved to' in line_lower:
                    logger.success(f"💾 {line}")
                elif line.startswith('Epoch'):
                    logger.info(f"🔄 {line}")
                elif any(char.isdigit() for char in line) and ('loss' in line_lower or 'map' in line_lower):
                    logger.info(f"📈 {line}")
                else:
                    logger.info(f"   {line}")

        # 等待进程结束
        return_code = process.wait()

        if return_code == 0:
            logger.success(f"✅ {description} 成功完成")
            return True
        else:
            logger.error(f"❌ {description} 失败，返回码: {return_code}")
            return False

    except Exception as e:
        logger.error(f"❌ 执行命令时出错: {e}")
        return False


def check_dependencies():
    """检查依赖包"""
    logger.info("🔍 检查依赖包...")
    
    required_packages = [
        "ultralytics",
        "torch",
        "torchvision", 
        "opencv-python",
        "pillow",
        "pyyaml",
        "loguru"
    ]
    
    missing_packages = []
    
    for package in required_packages:
        try:
            __import__(package.replace('-', '_'))
            logger.info(f"✅ {package}")
        except ImportError:
            logger.warning(f"❌ {package} 未安装")
            missing_packages.append(package)
    
    if missing_packages:
        logger.warning("缺少以下依赖包:")
        for package in missing_packages:
            logger.warning(f"  - {package}")
        
        install_cmd = f"pip install {' '.join(missing_packages)}"
        logger.info(f"可以使用以下命令安装: {install_cmd}")
        
        response = input("是否现在安装缺少的依赖包? (y/n): ")
        if response.lower() == 'y':
            if run_command(install_cmd, "安装依赖包"):
                logger.success("依赖包安装完成")
            else:
                logger.error("依赖包安装失败")
                return False
        else:
            logger.error("请先安装缺少的依赖包")
            return False
    
    return True


def check_data_files():
    """检查数据文件"""
    logger.info("🔍 检查数据文件...")
    
    images_dir = Path("data/extracted_frames")
    labels_dir = Path("data/annotated_frames")
    classes_file = labels_dir / "classes.txt"
    
    if not images_dir.exists():
        logger.error(f"图片目录不存在: {images_dir}")
        return False
    
    if not labels_dir.exists():
        logger.error(f"标注目录不存在: {labels_dir}")
        return False
    
    if not classes_file.exists():
        logger.error(f"类别文件不存在: {classes_file}")
        return False
    
    # 统计文件数量
    image_files = list(images_dir.glob("*.jpg")) + list(images_dir.glob("*.png"))
    label_files = [f for f in labels_dir.glob("*.txt") if f.name != "classes.txt"]
    
    logger.info(f"找到 {len(image_files)} 张图片")
    logger.info(f"找到 {len(label_files)} 个标注文件")
    
    if len(image_files) == 0:
        logger.error("没有找到图片文件")
        return False
    
    if len(label_files) == 0:
        logger.error("没有找到标注文件")
        return False
    
    # 检查类别文件内容
    with open(classes_file, 'r') as f:
        classes = [line.strip() for line in f.readlines() if line.strip()]
    
    logger.info(f"检测类别: {classes}")
    
    if len(classes) == 0:
        logger.error("类别文件为空")
        return False
    
    return True


def main():
    """主函数"""
    parser = argparse.ArgumentParser(description="一键训练YOLOv8模型")
    parser.add_argument("--model", type=str, default="n", choices=["n", "s", "m", "l", "x"],
                       help="模型大小")
    parser.add_argument("--epochs", type=int, default=None,
                       help="训练轮数")
    parser.add_argument("--batch", type=int, default=None,
                       help="批次大小")
    parser.add_argument("--skip-prepare", action="store_true",
                       help="跳过数据准备步骤")
    parser.add_argument("--prepare-only", action="store_true",
                       help="只进行数据准备，不训练")
    
    args = parser.parse_args()
    
    logger.info("🚀 YOLOv8一键训练脚本")
    logger.info("=" * 50)
    
    # 1. 检查依赖
    if not check_dependencies():
        logger.error("依赖检查失败，退出")
        return 1
    
    # 2. 检查数据文件
    if not check_data_files():
        logger.error("数据文件检查失败，退出")
        return 1
    
    # 3. 准备数据集
    if not args.skip_prepare:
        logger.info("📋 步骤1: 准备数据集")
        if not run_command("python prepare_dataset.py", "数据集准备"):
            logger.error("数据集准备失败，退出")
            return 1
    else:
        logger.info("⏭️ 跳过数据集准备步骤")
    
    # 如果只是准备数据，则退出
    if args.prepare_only:
        logger.success("✅ 数据准备完成")
        return 0
    
    # 4. 检查数据集是否准备好
    dataset_yaml = Path("data/yolo_dataset/data.yaml")
    if not dataset_yaml.exists():
        logger.error(f"数据集配置文件不存在: {dataset_yaml}")
        logger.error("请先运行数据准备步骤")
        return 1
    
    # 5. 开始训练
    logger.info("🎯 步骤2: 开始训练模型")

    # 构建训练命令
    train_cmd = f"python train_yolo.py --data {dataset_yaml} --model {args.model}"

    if args.epochs is not None:
        train_cmd += f" --epochs {args.epochs}"

    if args.batch is not None:
        train_cmd += f" --batch {args.batch}"

    logger.info("训练参数:")
    logger.info(f"  模型大小: YOLOv8{args.model}")
    logger.info(f"  数据集: {dataset_yaml}")
    if args.epochs:
        logger.info(f"  训练轮数: {args.epochs}")
    if args.batch:
        logger.info(f"  批次大小: {args.batch}")

    logger.info("开始训练，这可能需要一些时间...")
    logger.info("您将看到实时的训练进度和损失值")

    if not run_command(train_cmd, "模型训练", show_realtime=True):
        logger.error("模型训练失败")
        return 1
    
    # 6. 训练完成
    logger.success("🎉 训练流程完成!")
    
    # 查找生成的模型文件
    results_dir = Path("yolo_training")
    if results_dir.exists():
        model_dirs = list(results_dir.glob("yolov8*_custom*"))
        if model_dirs:
            latest_dir = max(model_dirs, key=lambda x: x.stat().st_mtime)
            best_model = latest_dir / "weights" / "best.pt"
            if best_model.exists():
                logger.success(f"最佳模型: {best_model}")
                logger.info("可以使用以下命令测试模型:")
                logger.info(f"python -c \"from ultralytics import YOLO; model = YOLO('{best_model}'); model.predict('path/to/test/image.jpg', show=True)\"")
    
    logger.info("训练日志和模型文件保存在 yolo_training/ 目录中")
    
    return 0


if __name__ == "__main__":
    exit(main())
