#!/usr/bin/env python3
"""
YOLOv8训练脚本
针对小数据集优化的训练配置和参数
"""

import os
import torch
import yaml
from pathlib import Path
from loguru import logger
from ultralytics import YOLO
import argparse


def check_environment():
    """检查训练环境"""
    logger.info("🔍 检查训练环境...")
    
    # 检查CUDA
    if torch.cuda.is_available():
        gpu_count = torch.cuda.device_count()
        gpu_name = torch.cuda.get_device_name(0)
        gpu_memory = torch.cuda.get_device_properties(0).total_memory / 1024**3
        logger.success(f"✅ CUDA可用: {gpu_count} GPU(s)")
        logger.info(f"   GPU: {gpu_name}")
        logger.info(f"   显存: {gpu_memory:.1f} GB")
        device = "0"  # 使用第一个GPU
    else:
        logger.warning("⚠️ CUDA不可用，将使用CPU训练（速度较慢）")
        device = "cpu"
    
    # 检查ultralytics版本
    try:
        from ultralytics import __version__
        logger.info(f"Ultralytics版本: {__version__}")
    except:
        logger.warning("无法获取ultralytics版本信息")
    
    return device


def get_optimal_batch_size(device: str, img_size: int = 640):
    """根据设备和图像大小推荐批次大小"""
    if device == "cpu":
        return 4  # CPU使用小批次
    
    try:
        # 获取GPU显存
        gpu_memory_gb = torch.cuda.get_device_properties(0).total_memory / 1024**3
        
        # 根据显存大小推荐批次大小
        if gpu_memory_gb >= 24:  # RTX 4090, A100等
            return 16
        elif gpu_memory_gb >= 12:  # RTX 4070Ti, RTX 3080等
            return 8
        elif gpu_memory_gb >= 8:   # RTX 4060Ti, RTX 3070等
            return 4
        else:  # 8GB以下
            return 2
    except:
        return 4  # 默认值


def create_training_config(dataset_yaml: str, device: str, small_dataset: bool = True):
    """创建训练配置"""
    
    # 获取最优批次大小
    batch_size = get_optimal_batch_size(device)
    
    # 小数据集优化配置
    if small_dataset:
        config = {
            # 基础参数
            'data': dataset_yaml,
            'epochs': 300,  # 小数据集需要更多轮次
            'batch': batch_size,
            'imgsz': 640,
            'device': device,
            
            # 学习率设置（小数据集使用较小学习率）
            'lr0': 0.001,  # 初始学习率
            'lrf': 0.01,   # 最终学习率比例
            
            # 正则化（防止过拟合）
            'weight_decay': 0.0005,
            'dropout': 0.1,  # 添加dropout
            
            # 数据增强（小数据集需要更强的增强）
            'hsv_h': 0.015,      # 色调变化
            'hsv_s': 0.7,        # 饱和度变化
            'hsv_v': 0.4,        # 亮度变化
            'degrees': 10.0,     # 旋转角度
            'translate': 0.1,    # 平移
            'scale': 0.5,        # 缩放
            'shear': 2.0,        # 剪切
            'perspective': 0.0,  # 透视变换
            'flipud': 0.0,       # 上下翻转
            'fliplr': 0.5,       # 左右翻转
            'mosaic': 1.0,       # 马赛克增强
            'mixup': 0.1,        # 混合增强
            'copy_paste': 0.1,   # 复制粘贴增强
            
            # 训练策略
            'patience': 50,      # 早停耐心值
            'save_period': 10,   # 每10轮保存一次
            'cos_lr': True,      # 余弦学习率调度
            'close_mosaic': 20,  # 最后20轮关闭mosaic
            
            # 其他设置
            'amp': True,         # 自动混合精度
            'fraction': 1.0,     # 使用全部数据
            'seed': 42,          # 随机种子
            'deterministic': True, # 确定性训练
            'val': True,         # 启用验证
            'plots': True,       # 生成训练图表
            'save': True,        # 保存模型
        }
    else:
        # 大数据集配置
        config = {
            'data': dataset_yaml,
            'epochs': 100,
            'batch': batch_size,
            'imgsz': 640,
            'device': device,
            'lr0': 0.01,
            'lrf': 0.01,
            'weight_decay': 0.0005,
            'patience': 30,
            'save_period': -1,
            'cos_lr': True,
            'amp': True,
            'val': True,
            'plots': True,
            'save': True,
        }
    
    return config


def train_model(config: dict, model_size: str = "n", project_name: str = "yolo_training"):
    """训练YOLO模型"""

    logger.info(f"🚀 开始训练YOLOv8{model_size}模型")
    logger.info("=" * 60)
    logger.info("📋 训练配置详情:")

    # 分类显示配置信息
    basic_config = ['data', 'epochs', 'batch', 'imgsz', 'device']
    learning_config = ['lr0', 'lrf', 'weight_decay', 'dropout']
    augmentation_config = ['hsv_h', 'hsv_s', 'hsv_v', 'degrees', 'translate', 'scale', 'fliplr', 'mosaic', 'mixup']

    logger.info("  🎯 基础参数:")
    for key in basic_config:
        if key in config:
            logger.info(f"    {key}: {config[key]}")

    logger.info("  📚 学习参数:")
    for key in learning_config:
        if key in config:
            logger.info(f"    {key}: {config[key]}")

    logger.info("  🎨 数据增强:")
    for key in augmentation_config:
        if key in config:
            logger.info(f"    {key}: {config[key]}")

    logger.info("  ⚙️ 其他设置:")
    for key, value in config.items():
        if key not in basic_config + learning_config + augmentation_config:
            logger.info(f"    {key}: {value}")

    logger.info("=" * 60)

    try:
        # 加载预训练模型
        model_name = f"yolov8{model_size}.pt"
        logger.info(f"📥 下载/加载预训练模型: {model_name}")

        import time
        start_load_time = time.time()
        model = YOLO(model_name)
        load_time = time.time() - start_load_time
        logger.success(f"✅ 模型加载完成 ({load_time:.2f}秒)")

        # 显示模型信息
        logger.info("🔍 模型信息:")
        logger.info(f"  模型大小: YOLOv8{model_size}")
        try:
            param_count = sum(p.numel() for p in model.model.parameters())
            logger.info(f"  参数数量: {param_count:,}")
        except:
            logger.info("  参数数量: 无法获取")

        # 开始训练
        logger.info("🎯 开始训练过程...")
        logger.info("训练日志将实时显示，请耐心等待...")
        logger.info("=" * 60)

        start_train_time = time.time()
        results = model.train(
            project=project_name,
            name=f"yolov8{model_size}_custom",
            verbose=True,  # 启用详细输出
            **config
        )

        train_time = time.time() - start_train_time

        logger.success("🎉 训练完成!")
        logger.info("=" * 60)
        logger.info(f"⏱️ 总训练时间: {train_time/60:.1f} 分钟 ({train_time:.1f} 秒)")

        # 显示训练结果
        logger.info("📊 训练结果摘要:")
        try:
            if hasattr(results, 'results_dict'):
                for key, value in results.results_dict.items():
                    logger.info(f"  {key}: {value}")
            elif hasattr(results, 'maps'):
                logger.info(f"  mAP50: {results.maps[0]:.4f}")
                logger.info(f"  mAP50-95: {results.maps[1]:.4f}")
        except Exception as e:
            logger.warning(f"无法获取详细训练结果: {e}")

        # 查找并显示模型文件
        project_path = Path(project_name)
        run_name = f"yolov8{model_size}_custom"

        # 查找最新的运行目录（可能有数字后缀）
        run_dirs = list(project_path.glob(f"{run_name}*"))
        if run_dirs:
            latest_run = max(run_dirs, key=lambda x: x.stat().st_mtime)
            weights_dir = latest_run / "weights"

            logger.info("📁 生成的文件:")
            logger.info(f"  训练目录: {latest_run}")

            if weights_dir.exists():
                best_model = weights_dir / "best.pt"
                last_model = weights_dir / "last.pt"

                if best_model.exists():
                    model_size_mb = best_model.stat().st_size / (1024 * 1024)
                    logger.success(f"  ✅ 最佳模型: {best_model} ({model_size_mb:.1f} MB)")

                if last_model.exists():
                    logger.info(f"  📄 最后模型: {last_model}")

            # 检查其他生成的文件
            results_png = latest_run / "results.png"
            if results_png.exists():
                logger.info(f"  📈 训练曲线: {results_png}")

            confusion_matrix = latest_run / "confusion_matrix.png"
            if confusion_matrix.exists():
                logger.info(f"  🔍 混淆矩阵: {confusion_matrix}")

            # 返回最佳模型路径
            if weights_dir.exists() and (weights_dir / "best.pt").exists():
                return str(weights_dir / "best.pt")

        logger.warning("未找到训练生成的模型文件")
        return None
            
    except Exception as e:
        logger.error(f"训练过程中出错: {e}")
        raise


def validate_dataset(dataset_yaml: str):
    """验证数据集配置"""
    logger.info("🔍 验证数据集配置...")
    
    if not Path(dataset_yaml).exists():
        raise FileNotFoundError(f"数据集配置文件不存在: {dataset_yaml}")
    
    with open(dataset_yaml, 'r') as f:
        data_config = yaml.safe_load(f)
    
    # 检查必要字段
    required_fields = ['path', 'train', 'val', 'nc', 'names']
    for field in required_fields:
        if field not in data_config:
            raise ValueError(f"数据集配置缺少必要字段: {field}")
    
    # 检查路径是否存在
    dataset_path = Path(data_config['path'])
    train_path = dataset_path / data_config['train']
    val_path = dataset_path / data_config['val']
    
    if not train_path.exists():
        raise FileNotFoundError(f"训练集路径不存在: {train_path}")
    if not val_path.exists():
        raise FileNotFoundError(f"验证集路径不存在: {val_path}")
    
    # 统计数据量
    train_images = len(list(train_path.glob("*.jpg"))) + len(list(train_path.glob("*.png")))
    val_images = len(list(val_path.glob("*.jpg"))) + len(list(val_path.glob("*.png")))
    
    logger.info(f"数据集验证通过:")
    logger.info(f"  类别数: {data_config['nc']}")
    logger.info(f"  类别名: {list(data_config['names'].values())}")
    logger.info(f"  训练集: {train_images} 张图片")
    logger.info(f"  验证集: {val_images} 张图片")
    
    return train_images + val_images < 100  # 判断是否为小数据集


def main():
    """主函数"""
    parser = argparse.ArgumentParser(description="YOLOv8训练脚本")
    parser.add_argument("--data", type=str, default="data/yolo_dataset/data.yaml", 
                       help="数据集配置文件路径")
    parser.add_argument("--model", type=str, default="n", choices=["n", "s", "m", "l", "x"],
                       help="模型大小 (n=nano, s=small, m=medium, l=large, x=xlarge)")
    parser.add_argument("--project", type=str, default="yolo_training",
                       help="项目名称")
    parser.add_argument("--epochs", type=int, default=None,
                       help="训练轮数（覆盖默认值）")
    parser.add_argument("--batch", type=int, default=None,
                       help="批次大小（覆盖自动检测）")
    
    args = parser.parse_args()
    
    logger.info("🎯 YOLOv8训练脚本启动")
    
    try:
        # 检查环境
        device = check_environment()
        
        # 验证数据集
        is_small_dataset = validate_dataset(args.data)
        
        # 创建训练配置
        config = create_training_config(args.data, device, is_small_dataset)
        
        # 应用命令行参数覆盖
        if args.epochs is not None:
            config['epochs'] = args.epochs
        if args.batch is not None:
            config['batch'] = args.batch
        
        # 开始训练
        best_model_path = train_model(config, args.model, args.project)
        
        if best_model_path:
            logger.success(f"🎉 训练成功完成!")
            logger.info(f"最佳模型: {best_model_path}")
            logger.info("可以使用以下命令进行推理测试:")
            logger.info(f"python -c \"from ultralytics import YOLO; model = YOLO('{best_model_path}'); model.predict('path/to/test/image.jpg', show=True)\"")
        
    except Exception as e:
        logger.error(f"❌ 训练失败: {e}")
        return 1
    
    return 0


if __name__ == "__main__":
    exit(main())
