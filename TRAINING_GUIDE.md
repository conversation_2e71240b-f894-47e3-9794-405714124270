# YOLOv8训练指南

## 🎯 概述

本指南将帮助您使用已标注的数据训练自定义的YOLOv8模型，用于检测手部和三种不同类型的容器。

## 📋 数据集信息

- **图片数量**: 48张
- **标注文件**: 49个
- **类别映射**:
  - 0: blue_box (蓝色箱子 - OK动作)
  - 1: upper_yellow_box (上层黄色箱子 - 电气缺陷)
  - 2: lower_yellow_box (下层黄色箱子 - 外观缺陷)
  - 3: hand (手部)

## 🚀 快速开始

### 方法1: 一键训练（推荐）

```bash
# 安装依赖并开始完整训练流程
python run_training.py

# 使用特定参数
python run_training.py --model s --epochs 200
```

### 方法2: 分步执行

```bash
# 步骤1: 准备数据集
python prepare_dataset.py

# 步骤2: 开始训练
python train_yolo.py --data data/yolo_dataset/data.yaml --model n
```

## ⚙️ 训练参数说明

### 模型大小选择
- `n` (nano): 最小最快，适合CPU或低端GPU
- `s` (small): 平衡性能和速度
- `m` (medium): 更好的精度
- `l` (large): 高精度，需要更多资源
- `x` (xlarge): 最高精度，需要强大GPU

### 小数据集优化配置

针对您的48张图片数据集，系统会自动应用以下优化：

```yaml
# 训练轮数
epochs: 300  # 小数据集需要更多轮次

# 学习率
lr0: 0.001   # 较小的初始学习率
lrf: 0.01    # 最终学习率比例

# 数据增强（增强版）
hsv_h: 0.015      # 色调变化
hsv_s: 0.7        # 饱和度变化
hsv_v: 0.4        # 亮度变化
degrees: 10.0     # 旋转角度
translate: 0.1    # 平移
scale: 0.5        # 缩放
fliplr: 0.5       # 左右翻转
mosaic: 1.0       # 马赛克增强
mixup: 0.1        # 混合增强

# 正则化
weight_decay: 0.0005
dropout: 0.1      # 防止过拟合

# 早停机制
patience: 50      # 50轮无改善则停止
```

## 📁 目录结构

训练完成后的目录结构：

```
yolo_detect_ws/
├── data/
│   ├── extracted_frames/     # 原始图片
│   ├── annotated_frames/     # 标注文件
│   └── yolo_dataset/         # YOLO格式数据集
│       ├── train/
│       │   ├── images/
│       │   └── labels/
│       ├── val/
│       │   ├── images/
│       │   └── labels/
│       └── data.yaml
├── yolo_training/            # 训练结果
│   └── yolov8n_custom/
│       ├── weights/
│       │   ├── best.pt       # 最佳模型
│       │   └── last.pt       # 最后一轮模型
│       └── results.png       # 训练曲线图
└── models/                   # 模型存放目录
```

## 🔧 训练命令详解

### 基础训练
```bash
python train_yolo.py --data data/yolo_dataset/data.yaml --model n
```

### 自定义参数训练
```bash
python train_yolo.py \
    --data data/yolo_dataset/data.yaml \
    --model s \
    --epochs 200 \
    --batch 8
```

### 命令行参数说明
- `--data`: 数据集配置文件路径
- `--model`: 模型大小 (n/s/m/l/x)
- `--epochs`: 训练轮数
- `--batch`: 批次大小
- `--project`: 项目名称

## 📊 训练监控

训练过程中会显示：
- 损失函数变化
- mAP (平均精度)
- 精确率和召回率
- 训练速度

训练结果保存在 `yolo_training/yolov8n_custom/` 目录中。

## 🎯 模型评估

训练完成后，可以使用以下方法评估模型：

```python
from ultralytics import YOLO

# 加载最佳模型
model = YOLO('yolo_training/yolov8n_custom/weights/best.pt')

# 在验证集上评估
results = model.val()

# 查看结果
print(f"mAP50: {results.box.map50}")
print(f"mAP50-95: {results.box.map}")
```

## 🔍 模型测试

```python
from ultralytics import YOLO

# 加载训练好的模型
model = YOLO('yolo_training/yolov8n_custom/weights/best.pt')

# 预测单张图片
results = model.predict('path/to/test/image.jpg', show=True)

# 预测并保存结果
results = model.predict('path/to/test/image.jpg', save=True)
```

## ⚠️ 注意事项

### 小数据集训练建议

1. **使用预训练模型**: 从COCO预训练的模型开始
2. **增加训练轮数**: 建议300轮以上
3. **强化数据增强**: 使用mosaic、mixup等增强技术
4. **降低学习率**: 使用较小的初始学习率
5. **早停机制**: 防止过拟合
6. **正则化**: 使用dropout和weight decay

### 硬件要求

- **最低配置**: 4GB GPU显存或8GB RAM (CPU)
- **推荐配置**: 8GB+ GPU显存
- **训练时间**: 
  - CPU: 2-4小时
  - GPU (RTX 3070): 30-60分钟

### 常见问题

1. **显存不足**: 减小batch_size或使用更小的模型
2. **训练过慢**: 使用GPU或减少epochs
3. **过拟合**: 增加数据增强或使用更多正则化
4. **精度不高**: 增加训练轮数或使用更大的模型

## 📈 性能优化建议

1. **数据质量**: 确保标注准确性
2. **数据平衡**: 各类别样本数量尽量均衡
3. **图像质量**: 使用清晰、多样化的图像
4. **超参数调优**: 根据验证结果调整学习率等参数

## 🔄 模型更新

当有新的标注数据时：

1. 将新图片放入 `data/extracted_frames/`
2. 将新标注放入 `data/annotated_frames/`
3. 重新运行训练流程
4. 或使用增量训练（从已有模型继续训练）

## 📞 获取帮助

如果遇到问题，请检查：
1. 数据路径是否正确
2. 依赖包是否完整安装
3. GPU驱动是否正常
4. 标注格式是否正确

训练完成后，最佳模型将保存为 `best.pt`，可以直接用于推理和部署。
