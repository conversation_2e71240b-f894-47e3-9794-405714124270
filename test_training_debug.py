#!/usr/bin/env python3
"""
测试训练调试功能的脚本
模拟训练过程的输出，验证调试信息是否正常显示
"""

import time
import sys
from loguru import logger


def simulate_training_output():
    """模拟训练过程的输出"""
    
    # 模拟不同类型的训练输出
    training_messages = [
        "🚀 开始训练YOLOv8n模型",
        "=" * 60,
        "📋 训练配置详情:",
        "  🎯 基础参数:",
        "    data: data/yolo_dataset/data.yaml",
        "    epochs: 300",
        "    batch: 4",
        "    imgsz: 640",
        "    device: 0",
        "  📚 学习参数:",
        "    lr0: 0.001",
        "    lrf: 0.01",
        "    weight_decay: 0.0005",
        "    dropout: 0.1",
        "=" * 60,
        "📥 下载/加载预训练模型: yolov8n.pt",
        "Downloading https://github.com/ultralytics/assets/releases/download/v0.0.0/yolov8n.pt to yolov8n.pt...",
        "100%|██████████| 6.23M/6.23M [00:01<00:00, 5.85MB/s]",
        "✅ 模型加载完成 (2.34秒)",
        "🔍 模型信息:",
        "  模型大小: YOLOv8n",
        "  参数数量: 3,157,200",
        "🎯 开始训练过程...",
        "训练日志将实时显示，请耐心等待...",
        "=" * 60,
        "Ultralytics YOLOv8.0.196 🚀 Python-3.8.10 torch-2.0.1+cu118 CUDA:0 (NVIDIA GeForce RTX 3070, 8192MiB)",
        "⚙️ optimizer: AdamW(lr=0.001) with parameter groups 57 weight(decay=0.0), 64 weight(decay=0.0005), 63 bias",
        "🎯 train: Scanning /data/yolo_dataset/train/labels... 40 images, 0 backgrounds, 0 corrupt",
        "🔍 val: Scanning /data/yolo_dataset/val/labels... 8 images, 0 backgrounds, 0 corrupt",
        "Plotting labels to runs/detect/train/labels.jpg... ",
        "🔄 Epoch 1/300",
        "📊       Class     Images  Instances          P          R      mAP50   mAP50-95",
        "📊         all         48        156      0.892      0.756      0.834      0.542",
        "📊   blue_box         48         45      0.891      0.778      0.856      0.623",
        "📊   upper_yellow_box  48         38      0.889      0.737      0.821      0.498",
        "📊   lower_yellow_box  48         42      0.894      0.762      0.835      0.521",
        "📊        hand         48         31      0.894      0.742      0.823      0.525",
        "🔄 Epoch 2/300",
        "📈      2/300      1.23G      2.456      1.234      0.987      0.654     40     640",
        "📊       Class     Images  Instances          P          R      mAP50   mAP50-95",
        "📊         all         48        156      0.901      0.768      0.847      0.558",
        "⏱️ 运行时间: 0.5 分钟 - 进程仍在运行...",
        "🔄 Epoch 5/300",
        "📈      5/300      1.23G      2.123      1.098      0.876      0.687     40     640",
        "📊       Class     Images  Instances          P          R      mAP50   mAP50-95",
        "📊         all         48        156      0.915      0.782      0.863      0.574",
        "💾 Results saved to runs/detect/train",
        "✅ 训练完成!",
        "🎉 训练完成!",
        "=" * 60,
        "⏱️ 总训练时间: 45.2 分钟 (2712.3 秒)",
        "📊 训练结果摘要:",
        "  mAP50: 0.8634",
        "  mAP50-95: 0.5742",
        "📁 生成的文件:",
        "  训练目录: yolo_training/yolov8n_custom",
        "  ✅ 最佳模型: yolo_training/yolov8n_custom/weights/best.pt (12.4 MB)",
        "  📄 最后模型: yolo_training/yolov8n_custom/weights/last.pt",
        "  📈 训练曲线: yolo_training/yolov8n_custom/results.png",
        "  🔍 混淆矩阵: yolo_training/yolov8n_custom/confusion_matrix.png"
    ]
    
    logger.info("🎬 开始模拟训练输出...")
    logger.info("这将展示增强的调试信息如何显示不同类型的训练消息")
    logger.info("=" * 60)
    
    for i, message in enumerate(training_messages):
        print(message)  # 直接打印，模拟训练脚本的输出
        
        # 模拟不同的延迟
        if "Epoch" in message:
            time.sleep(0.5)  # epoch信息稍微慢一点
        elif "downloading" in message.lower():
            time.sleep(1.0)  # 下载信息慢一点
        elif message.startswith("="):
            time.sleep(0.2)  # 分隔符快一点
        else:
            time.sleep(0.1)  # 其他信息正常速度
        
        # 每10条消息暂停一下
        if (i + 1) % 10 == 0:
            time.sleep(0.5)
    
    logger.success("🎉 模拟训练输出完成!")


def test_error_handling():
    """测试错误处理"""
    logger.info("\n🧪 测试错误处理...")
    
    error_messages = [
        "ERROR: CUDA out of memory",
        "WARNING: Dataset appears to be empty",
        "Exception in training loop: ValueError",
        "Failed to load model weights",
        "Traceback (most recent call last):"
    ]
    
    for message in error_messages:
        print(message)
        time.sleep(0.3)


def main():
    """主函数"""
    logger.info("🧪 训练调试功能测试")
    logger.info("这个脚本将模拟训练过程的各种输出，")
    logger.info("展示增强的调试信息如何分类和显示不同类型的消息")
    
    try:
        simulate_training_output()
        test_error_handling()
        
        logger.success("✅ 调试功能测试完成!")
        logger.info("\n现在您可以运行实际的训练命令:")
        logger.info("python run_training.py")
        logger.info("\n您将看到类似的彩色分类输出，包括:")
        logger.info("📊 训练指标 (epoch, loss, mAP等)")
        logger.info("🎯 训练状态 (training, validating等)")
        logger.info("📥 模型加载信息")
        logger.info("✅ 成功消息")
        logger.info("❌ 错误消息")
        logger.info("⚠️ 警告消息")
        logger.info("⏱️ 定期的进度更新")
        
    except KeyboardInterrupt:
        logger.warning("⚠️ 测试被用户中断")
    except Exception as e:
        logger.error(f"❌ 测试过程中出错: {e}")


if __name__ == "__main__":
    main()
